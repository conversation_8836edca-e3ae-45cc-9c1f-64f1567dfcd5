# app.py
import os
import json
from datetime import datetime, timezone
from flask import Flask, request, jsonify, abort
import pandas as pd

# -------- Config --------
# Set a secret so randoms can't hit your endpoint.
#   export WEBHOOK_TOKEN="change-me"
WEBHOOK_TOKEN = os.getenv("WEBHOOK_TOKEN", "change-me")
PORT = int(os.getenv("PORT", "5000"))
CSV_PATH = os.getenv("CSV_PATH", "signals.csv")

app = Flask(__name__)


def to_iso_utc(ms_since_epoch):
    try:
        return datetime.fromtimestamp(
            int(ms_since_epoch) / 1000, tz=timezone.utc
        ).isoformat()
    except Exception:
        return None


@app.post("/tvwebhook")
def tvwebhook():
    # ---- Simple auth via custom header ----
    # In your TradingView alert set a header:  X-Webhook-Token: <your secret>
    if request.headers.get("X-Webhook-Token") != WEBHOOK_TOKEN:
        abort(401, description="bad token")

    # ---- Parse body (JSON or raw text) ----
    data = request.get_json(silent=True)
    if data is None:
        # TradingView can send plain text if you put plain text in Message
        raw = request.get_data(as_text=True) or ""
        try:
            data = json.loads(raw)
        except Exception:
            # If it's not JSON, wrap as text so we still log it
            data = {"_raw": raw}

    # ---- Normalize a few useful fields ----
    alert = data.get("alert")
    ticker = data.get("ticker")
    tf = data.get("tf") or data.get("interval")
    ohlcv = data.get("ohlcv") or {}
    bartime_ms = data.get("bartime") or data.get("time")
    bartime_iso = to_iso_utc(bartime_ms) if bartime_ms is not None else None

    row = {
        "ts_received": datetime.now(timezone.utc).isoformat(),
        "alert": alert,
        "ticker": ticker,
        "tf": tf,
        "open": ohlcv.get("open"),
        "high": ohlcv.get("high"),
        "low": ohlcv.get("low"),
        "close": ohlcv.get("close"),
        "volume": ohlcv.get("volume"),
        "bartime_ms": bartime_ms,
        "bartime_iso": bartime_iso,
    }

    # ---- Append to CSV (creates on first run) ----
    try:
        df = pd.DataFrame([row])
        header = not os.path.exists(CSV_PATH)
        df.to_csv(CSV_PATH, mode="a", index=False, header=header)
    except Exception as e:
        # Don’t fail the webhook if logging hiccups
        print(f"[warn] failed to write CSV: {e}")

    # ---- Return fast (TradingView prefers quick 2xx) ----
    return jsonify({"status": "ok"}), 200


if __name__ == "__main__":
    # 0.0.0.0 so ngrok can reach it
    app.run(host="0.0.0.0", port=PORT)
